"""
文字渲染器
负责译文绘制和最终图像合成
"""
import os
import cv2
import numpy as np
from typing import List, Tuple, Dict, Any
from PIL import Image, ImageDraw, ImageFont

from models.data_models import TranslationResult, LayoutResult, RenderConfig, ProcessingResult
from config.settings import get_config_manager


class Renderer:
    """文字渲染器"""

    def __init__(self, weight_adjustment: int = 400):
        """
        初始化渲染器

        Args:
            weight_adjustment: 可变字体粗细调节值 (100-900)
        """
        self.config_manager = get_config_manager()
        self.weight_adjustment = weight_adjustment

        # 初始化布局处理器（复用实例）
        from processors.layout_processor import LayoutProcessor
        self.layout_processor = LayoutProcessor()

        print(f"渲染器初始化完成，字体粗细设置: {weight_adjustment}")
    
    def render_translations(
        self,
        base_image: np.ndarray,
        translation_results: List[TranslationResult],
        layout_result: LayoutResult,
        original_image_path: str = None
    ) -> ProcessingResult:
        """
        渲染翻译文字到图像上
        
        Args:
            base_image: 基础图像（已去除原文字）
            translation_results: 翻译结果列表
            layout_result: 布局分析结果
            original_image_path: 原始图像路径（用于调试）
            
        Returns:
            ProcessingResult: 包含最终图像和渲染日志的结果
        """
        try:
            print(f"开始渲染 {len(translation_results)} 个翻译文字")
            
            # 计算每个翻译的渲染配置
            render_configs = self._calculate_render_configs(
                translation_results, layout_result
            )
            
            # 生成翻译调试图像（在实际渲染前）
            if self.config_manager.config.enable_translation_debug and original_image_path:
                self._generate_translation_debug_image(
                    original_image_path, translation_results, render_configs, layout_result
                )
            
            # 转换为PIL图像进行文字渲染
            pil_image = Image.fromarray(cv2.cvtColor(base_image, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(pil_image)
            
            render_log = []
            
            # 渲染每个翻译文字
            for result, config in zip(translation_results, render_configs):
                try:
                    self._render_single_text(draw, result, config)
                    
                    render_log.append({
                        'original': result.original_text,
                        'translated': result.translated_text,
                        'font': result.font_info.matched_font,
                        'position': (config.text_x, config.text_y),
                        'size': config.font_size
                    })
                    
                    print(f"已渲染: '{result.original_text}' → '{result.translated_text}' "
                          f"at ({config.text_x}, {config.text_y})")
                    
                except Exception as e:
                    print(f"渲染单个文字失败 '{result.original_text}': {e}")
                    continue
            
            # 转换回OpenCV格式
            final_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            
            result_data = {
                'image': final_image,
                'render_log': render_log
            }
            
            print(f"渲染完成，成功渲染 {len(render_log)} 个文字")
            return ProcessingResult.success_result(result_data)
            
        except Exception as e:
            error_msg = f"渲染失败: {str(e)}"
            print(error_msg)
            return ProcessingResult.error_result(error_msg)
    
    def _calculate_render_configs(
        self,
        translation_results: List[TranslationResult],
        layout_result: LayoutResult
    ) -> List[RenderConfig]:
        """计算渲染配置"""
        configs = []
        
        for result in translation_results:
            try:
                config = self._calculate_single_render_config(result, layout_result)
                configs.append(config)
            except Exception as e:
                print(f"计算渲染配置失败 '{result.original_text}': {e}")
                # 使用默认配置
                x, y, w, h = result.bbox
                configs.append(RenderConfig(
                    text_x=x,
                    text_y=y,
                    text_width=w,
                    text_height=h,
                    font_size=result.style_info.estimated_font_size,
                    font_path=result.font_info.font_path,
                    alignment_type='center',
                    color=result.style_info.color
                ))
        
        return configs

    def _calculate_single_render_config(
        self,
        result: TranslationResult,
        layout_result: LayoutResult
    ) -> RenderConfig:
        """计算单个文字的渲染配置"""
        x, y, w, h = result.bbox

        # 计算字体大小
        font_size = self._calculate_font_size(result)

        # 获取文字尺寸
        text_width, text_height = self._get_text_dimensions(
            result.translated_text, result.font_info.font_path, font_size
        )

        # 确定对齐方式
        alignment_type = self._determine_alignment(result, layout_result)

        # 计算文字位置
        text_x, text_y = self._calculate_text_position(
            x, y, w, h, text_width, text_height, alignment_type
        )

        return RenderConfig(
            text_x=text_x,
            text_y=text_y,
            text_width=text_width,
            text_height=text_height,
            font_size=font_size,
            font_path=result.font_info.font_path,
            alignment_type=alignment_type,
            color=result.style_info.color
        )

    def _calculate_font_size(self, result: TranslationResult) -> int:
        """直接使用OCR阶段像素级高度统一的高度作为字号"""
        # 直接使用OCR阶段像素级高度统一的高度
        font_size = result.style_info.precise_height
        print(f"    使用OCR阶段统一高度: '{result.original_text}' → {font_size}px")
        return font_size

    def _get_text_dimensions(self, text: str, font_path: str, font_size: int) -> Tuple[int, int]:
        """获取文字尺寸"""
        try:
            # 创建字体对象
            if self.weight_adjustment != 400:
                # 可变字体设置
                font = ImageFont.truetype(font_path, font_size)
                if hasattr(font, 'set_variation_by_axes'):
                    font.set_variation_by_axes([self.weight_adjustment])
            else:
                font = ImageFont.truetype(font_path, font_size)

            # 创建临时图像计算文字尺寸
            temp_img = Image.new('RGB', (1, 1))
            temp_draw = ImageDraw.Draw(temp_img)
            try:
                bbox = temp_draw.textbbox((0, 0), text, font=font, anchor='lt')
            except TypeError:
                # Pillow < 8.0 不支持anchor参数
                bbox = temp_draw.textbbox((0, 0), text, font=font)

            width = bbox[2] - bbox[0]
            height = bbox[3] - bbox[1]

            return width, height

        except Exception as e:
            print(f"获取文字尺寸失败: {e}")
            # 返回估算尺寸
            return len(text) * font_size // 2, font_size

    def _determine_alignment(self, result: TranslationResult, layout_result) -> str:
        """确定对齐方式"""
        try:
            # 如果翻译对象有text_align属性，直接使用Gemini的分析结果
            if hasattr(result, 'text_align') and result.text_align:
                return result.text_align

            # 否则使用布局处理器的方法来确定对齐方式
            return self.layout_processor.get_alignment_for_region(result.bbox, layout_result)

        except Exception as e:
            print(f"获取区域对齐方式失败: {e}")
            return 'center'

    def _calculate_text_position(
        self,
        orig_x: int, orig_y: int, orig_w: int, orig_h: int,
        text_width: int, text_height: int,
        alignment_type: str
    ) -> Tuple[int, int]:
        """计算文字位置"""
        # 垂直居中
        text_y = orig_y + (orig_h - text_height) // 2

        # 水平对齐
        if alignment_type == 'left':
            text_x = orig_x
        elif alignment_type == 'right':
            text_x = orig_x + orig_w - text_width
        else:  # 'center'
            text_x = orig_x + (orig_w - text_width) // 2

        # 确保位置不为负数
        text_x = max(0, text_x)
        text_y = max(0, text_y)

        return text_x, text_y

    def _render_single_text(
        self,
        draw: ImageDraw.Draw,
        result: TranslationResult,
        config: RenderConfig
    ):
        """渲染单个文字"""
        try:
            # 创建字体对象
            if self.weight_adjustment != 400:
                # 可变字体设置
                font = ImageFont.truetype(config.font_path, config.font_size)
                if hasattr(font, 'set_variation_by_axes'):
                    font.set_variation_by_axes([self.weight_adjustment])
            else:
                font = ImageFont.truetype(config.font_path, config.font_size)

            # 绘制文字
            try:
                draw.text(
                    (config.text_x, config.text_y),
                    result.translated_text,
                    font=font,
                    fill=config.color,
                    anchor='lt'
                )
            except TypeError:
                draw.text(
                    (config.text_x, config.text_y),
                    result.translated_text,
                    font=font,
                    fill=config.color
                )

        except Exception as e:
            print(f"渲染文字失败 '{result.translated_text}': {e}")
            # 尝试使用默认字体
            try:
                default_font = ImageFont.load_default()
                draw.text(
                    (config.text_x, config.text_y),
                    result.translated_text,
                    font=default_font,
                    fill=config.color
                )
            except Exception as e2:
                print(f"使用默认字体渲染也失败: {e2}")

    def save_final_image(self, image: np.ndarray, filename: str = "final_translated.png") -> str:
        """
        保存最终图像

        Args:
            image: 最终图像
            filename: 文件名

        Returns:
            str: 保存的文件路径
        """
        try:
            output_dir = self.config_manager.ensure_output_dir()
            output_path = f"{output_dir}/{filename}"

            cv2.imwrite(output_path, image)
            print(f"最终翻译结果已保存: {output_path}")

            return output_path

        except Exception as e:
            print(f"保存最终图像失败: {e}")
            return ""

    def _generate_translation_debug_image(
        self,
        original_image_path: str,
        translation_results: List[TranslationResult],
        render_configs: List[RenderConfig],
        layout_result: LayoutResult
    ):
        """
        生成翻译调试图像，包含原文检测框和译文文本框
        
        Args:
            original_image_path: 原始图像路径
            translation_results: 翻译结果列表
            render_configs: 渲染配置列表
            layout_result: 布局分析结果
        """
        try:
            print("生成翻译调试图像...")
            
            # 确保调试目录存在
            debug_dir = self.config_manager.config.translation_debug_dir
            os.makedirs(debug_dir, exist_ok=True)
            
            # 读取原始图像
            original_image = cv2.imread(original_image_path)
            if original_image is None:
                print(f"无法读取原始图像: {original_image_path}")
                return
            
            # 创建调试图像副本
            debug_image = original_image.copy()
            
            # 绘制原文检测框和译文文本框
            for result, config in zip(translation_results, render_configs):
                # 绘制原文检测框（绿色）
                x, y, w, h = result.bbox
                cv2.rectangle(debug_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
                
                # 绘制译文文本框（紫色）
                cv2.rectangle(debug_image, 
                             (config.text_x, config.text_y), 
                             (config.text_x + config.text_width, config.text_y + config.text_height), 
                             (255, 0, 255), 2)
                
                # 绘制中心点连线（黄色）
                orig_center_x = x + w // 2
                orig_center_y = y + h // 2
                trans_center_x = config.text_x + config.text_width // 2
                trans_center_y = config.text_y + config.text_height // 2
                cv2.line(debug_image, (orig_center_x, orig_center_y), (trans_center_x, trans_center_y), (0, 255, 255), 2)
                
                # 绘制中心点
                cv2.circle(debug_image, (orig_center_x, orig_center_y), 5, (0, 0, 255), -1)  # 红色原文中心
                cv2.circle(debug_image, (trans_center_x, trans_center_y), 5, (255, 0, 255), -1)  # 紫色译文中心
            
            # 转换为PIL图像进行中文标签绘制
            pil_image = Image.fromarray(cv2.cvtColor(debug_image, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(pil_image)
            
            # 准备绘制标签的字体
            label_font = self._load_label_font()
            
            # 在调试图像中绘制实际的译文文字（用于对比）
            for result, config in zip(translation_results, render_configs):
                try:
                    # 创建与实际渲染相同的字体对象
                    if self.weight_adjustment != 400:
                        font = ImageFont.truetype(config.font_path, config.font_size)
                        if hasattr(font, 'set_variation_by_axes'):
                            font.set_variation_by_axes([self.weight_adjustment])
                    else:
                        font = ImageFont.truetype(config.font_path, config.font_size)
                    
                    # 绘制实际译文（半透明红色，便于对比）
                    try:
                        draw.text(
                            (config.text_x, config.text_y),
                            result.translated_text,
                            font=font,
                            fill=(255, 0, 0, 128),
                            anchor='lt'
                        )
                    except TypeError:
                        draw.text(
                            (config.text_x, config.text_y),
                            result.translated_text,
                            font=font,
                            fill=(255, 0, 0, 128)
                        )
                    
                    # 计算实际文字的边界框
                    try:
                        actual_bbox = draw.textbbox((config.text_x, config.text_y), result.translated_text, font=font, anchor='lt')
                    except TypeError:
                        actual_bbox = draw.textbbox((config.text_x, config.text_y), result.translated_text, font=font)
                    actual_width = actual_bbox[2] - actual_bbox[0]
                    actual_height = actual_bbox[3] - actual_bbox[1]
                    
                    print(f"    实际渲染对比 '{result.original_text}':")
                    print(f"      预期尺寸: {config.text_width}x{config.text_height}")
                    print(f"      实际尺寸: {actual_width}x{actual_height}")
                    print(f"      尺寸差异: 宽度{actual_width-config.text_width:+d}px, 高度{actual_height-config.text_height:+d}px")
                    
                except Exception as e:
                    print(f"调试图像中绘制译文失败: {e}")
            
            # 添加文字标签和信息
            for result, config in zip(translation_results, render_configs):
                x, y, w, h = result.bbox
                
                # 计算偏移信息
                offset_x = config.text_x + config.text_width // 2 - (x + w // 2)
                offset_y = config.text_y + config.text_height // 2 - (y + h // 2)
                offset_distance = np.sqrt(offset_x**2 + offset_y**2)
                
                # 绘制文字信息（在原文框上方）
                info_y = y - 80
                if info_y < 0:
                    info_y = y + h + 10
                
                try:
                    if label_font:
                        draw.text((x, info_y), f"原文: {result.original_text}", fill=(255, 255, 255), font=label_font)
                        draw.text((x, info_y + 15), f"译文: {result.translated_text}", fill=(255, 255, 255), font=label_font)
                        draw.text((x, info_y + 30), f"字号: {config.font_size}px", fill=(255, 255, 255), font=label_font)
                        draw.text((x, info_y + 45), f"对齐: {config.alignment_type}", fill=(255, 255, 255), font=label_font)
                        draw.text((x, info_y + 60), f"偏移: ({offset_x:+d}, {offset_y:+d}) = {offset_distance:.1f}px", fill=(255, 255, 255), font=label_font)
                    else:
                        # 如果字体加载失败，使用英文
                        draw.text((x, info_y), f"Orig: {result.original_text}", fill=(255, 255, 255))
                        draw.text((x, info_y + 15), f"Trans: {result.translated_text}", fill=(255, 255, 255))
                        draw.text((x, info_y + 30), f"Size: {config.font_size}px", fill=(255, 255, 255))
                        draw.text((x, info_y + 45), f"Align: {config.alignment_type}", fill=(255, 255, 255))
                        draw.text((x, info_y + 60), f"Offset: ({offset_x:+d}, {offset_y:+d}) = {offset_distance:.1f}px", fill=(255, 255, 255))
                except Exception as e:
                    print(f"绘制文字标签失败: {e}")
            
            # 添加图例
            self._add_translation_debug_legend(draw, label_font)
            
            # 转换回OpenCV格式并保存
            final_debug_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            
            debug_path = os.path.join(debug_dir, "translation_debug.png")
            cv2.imwrite(debug_path, final_debug_image)
            print(f"翻译调试图已保存: {debug_path}")
            
            # 打印调试信息
            print("翻译调试信息:")
            for result, config in zip(translation_results, render_configs):
                print(f"  '{result.original_text}' → '{result.translated_text}':")
                print(f"    原文位置: {result.bbox}")
                print(f"    译文位置: ({config.text_x}, {config.text_y}, {config.text_width}, {config.text_height})")
                print(f"    字体大小: {config.font_size}px, 对齐方式: {config.alignment_type}")
                
        except Exception as e:
            print(f"生成翻译调试图像失败: {e}")

    def _load_label_font(self):
        """加载用于标签的字体"""
        try:
            import platform
            sys_name = platform.system()
            if sys_name == "Windows":
                return ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 14)
            elif sys_name == "Darwin":
                return ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", 14)
            else:
                return ImageFont.truetype("/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc", 14)
        except Exception:
            pass

        # 如果系统字体加载失败，尝试使用项目内字体
        project_fonts = {
            "思源黑体": "fonts/思源黑体/SourceHanSans-VF.otf",
            "台北黑体": "fonts/台北黑体/TaipeiSans-Bold.ttf",
            "NotoSans": "fonts/NotoSansSC/NotoSansSC-Black.ttf"
        }
        for font_name, font_path in project_fonts.items():
            if os.path.exists(font_path):
                try:
                    return ImageFont.truetype(font_path, 14)
                except Exception:
                    continue

        # 最后退回默认字体
        try:
            return ImageFont.load_default()
        except Exception:
            return None

    def _add_translation_debug_legend(self, draw: ImageDraw.Draw, label_font):
        """添加翻译调试图例"""
        try:
            # 创建图例背景
            legend_items = [
                "翻译调试图例:",
                "原文检测框（绿色）",
                "译文文本框（紫色）", 
                "原文中心（红色圆点）",
                "译文中心（紫色圆点）",
                "中心连线（黄色）"
            ]
            
            legend_y = 30
            legend_bg = Image.new('RGBA', (320, 130), (0, 0, 0, 200))
            
            if label_font:
                for i, item in enumerate(legend_items):
                    color = (255, 255, 255) if i == 0 else (200, 200, 200)
                    try:
                        draw.text((20, legend_y + i * 18), item, fill=color, font=label_font)
                    except:
                        draw.text((20, legend_y + i * 18), item, fill=color)
            
        except Exception as e:
            print(f"添加图例失败: {e}")
