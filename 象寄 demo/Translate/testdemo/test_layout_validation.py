#!/usr/bin/env python3
"""
智能图像翻译测试程序
使用OCR识别文字位置，然后让Gemini分析对齐分组和翻译需求，最后进行精确擦除和重绘
"""

import os
import sys
import json
import base64
import requests
from PIL import Image, ImageDraw, ImageFont
import cv2
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from collections import Counter

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from processors.layout_processor import LayoutProcessor
from processors.ocr_processor import OCRProcessor
from config.settings import ConfigManager

class SmartTranslationTester:
    """智能图像翻译测试器"""
    
    def __init__(self):
        self.api_key = "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"
        self.api_url = "https://openrouter.ai/api/v1/chat/completions"
        self.model = "google/gemini-2.5-pro-preview"
        
        # 初始化处理器
        self.config_manager = ConfigManager()
        self.ocr_processor = OCRProcessor()
        self.layout_processor = LayoutProcessor()
        
        # 设置输出目录
        self.output_dir = "testdemo/smart_translation_results"
        os.makedirs(self.output_dir, exist_ok=True)
        
        print(f"智能图像翻译测试器初始化完成")
        print(f"使用模型: {self.model}")
        print(f"输出目录: {self.output_dir}")
    
    def encode_image_to_base64(self, image_path: str) -> str:
        """将图片编码为base64"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    
    def extract_text_regions(self, image_path: str) -> Tuple[List, List]:
        """提取所有文本区域（中文和其他语言）"""
        print(f"\n开始OCR识别: {image_path}")
        
        # OCR识别
        ocr_result = self.ocr_processor.process_image(image_path)
        if not ocr_result.success:
            raise Exception(f"OCR处理失败: {ocr_result.error_message}")
        
        chinese_regions = ocr_result.data.chinese_regions
        other_regions = ocr_result.data.other_regions
        
        print(f"识别到 {len(chinese_regions)} 个中文区域")
        print(f"识别到 {len(other_regions)} 个其他语言区域")
        
        return chinese_regions, other_regions
    
    def analyze_colors_from_image(self, image_path: str, chinese_regions: List, other_regions: List) -> Dict[str, Any]:
        """使用主程序的颜色识别功能分析图像中的文字颜色"""
        print(f"\n🎨 开始程序颜色识别分析...")
        
        # 加载图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 无法加载图片: {image_path}")
            return {}
        
        color_analysis = {}
        
        # 分析中文区域的颜色
        for i, region in enumerate(chinese_regions):
            try:
                # 提取文字区域
                x, y, w, h = region.bbox
                x = max(0, x)
                y = max(0, y)
                w = min(w, image.shape[1] - x)
                h = min(h, image.shape[0] - y)
                
                if w <= 0 or h <= 0:
                    continue
                    
                text_region = image[y:y+h, x:x+w]
                
                # 使用主程序的颜色识别方法
                text_color, bg_color, is_dark_text = self.extract_text_foreground_color(text_region)
                
                # 转换为RGB格式（主程序返回的是RGB）
                color_analysis[region.text] = {
                    'region_id': i,
                    'text_color': text_color,
                    'background_color': bg_color,
                    'is_dark_text': is_dark_text,
                    'bbox': region.bbox
                }
                
                print(f"  📝 '{region.text}': 文字色{text_color}, 背景色{bg_color}, 深色文字{is_dark_text}")
                
            except Exception as e:
                print(f"  ❌ 颜色分析失败 '{region.text}': {e}")
                # 使用默认颜色
                color_analysis[region.text] = {
                    'region_id': i,
                    'text_color': (0, 0, 0),
                    'background_color': (255, 255, 255),
                    'is_dark_text': True,
                    'bbox': region.bbox
                }
        
        print(f"✅ 颜色识别完成，分析了 {len(color_analysis)} 个文字区域")
        return color_analysis
    
    # ===== 从主程序复制的颜色识别方法 =====
    
    def _sample_edge_pixels(self, binary_image: np.ndarray) -> np.ndarray:
        """采样边缘像素的二值化值来确定背景"""
        h, w = binary_image.shape
        edge_pixels = []
        
        # 采样边缘的宽度（像素）
        edge_width = max(1, min(3, min(h, w) // 10))  # 边缘宽度1-3像素，根据图像大小调整
        
        # 采样上边缘
        if h > edge_width:
            edge_pixels.extend(binary_image[:edge_width, :].flatten())
        
        # 采样下边缘
        if h > edge_width:
            edge_pixels.extend(binary_image[-edge_width:, :].flatten())
        
        # 采样左边缘（避免重复采样角落）
        if w > edge_width and h > 2 * edge_width:
            edge_pixels.extend(binary_image[edge_width:-edge_width, :edge_width].flatten())
        
        # 采样右边缘（避免重复采样角落）
        if w > edge_width and h > 2 * edge_width:
            edge_pixels.extend(binary_image[edge_width:-edge_width, -edge_width:].flatten())
        
        return np.array(edge_pixels)
    
    def _bgr_to_rgb(self, bgr_color) -> tuple:
        """将BGR颜色转换为RGB颜色"""
        try:
            if isinstance(bgr_color, np.ndarray):
                bgr_color = bgr_color.astype(int)
                return (int(bgr_color[2]), int(bgr_color[1]), int(bgr_color[0]))
            elif isinstance(bgr_color, (list, tuple)) and len(bgr_color) >= 3:
                return (int(bgr_color[2]), int(bgr_color[1]), int(bgr_color[0]))
            else:
                return (0, 0, 0)
        except Exception as e:
            print(f"BGR到RGB转换失败: {e}")
            return (0, 0, 0)
    
    def _get_dominant_color(self, pixels: np.ndarray, background_color: tuple = None, debug_print: bool = False) -> Tuple[int, int, int]:
        """获取像素区域的主要颜色 - 加权过滤平均色算法（距离背景色越远权重越高）"""
        if len(pixels) == 0:
            return (0, 0, 0)
        
        # 如果像素很少，直接用平均色
        if len(pixels) < 30:
            avg_color = np.mean(pixels, axis=0).astype(int)
            return self._bgr_to_rgb(avg_color)
        
        # 如果没有背景色，回退到普通平均色
        if background_color is None:
            avg_color = np.mean(pixels, axis=0).astype(int)
            return self._bgr_to_rgb(avg_color)
        
        # 颜色量化
        quantized = (pixels // 8) * 8  # 32级量化
        color_tuples = [tuple(pixel) for pixel in quantized]
        color_counter = Counter(color_tuples)
        
        # 获取前5个颜色
        top_colors = color_counter.most_common(5)
        if not top_colors:
            avg_color = np.mean(pixels, axis=0).astype(int)
            return self._bgr_to_rgb(avg_color)
        
        # 背景色转换为BGR用于距离计算
        bg_color_bgr = (background_color[2], background_color[1], background_color[0])
        
        # 过滤出距离背景色足够远的颜色，并计算权重
        valid_colors = []
        total_weight = 0
        distance_threshold = 40
        
        for color_bgr, count in top_colors:
            # 计算与背景色的距离
            distance = np.sqrt(sum((a - b) ** 2 for a, b in zip(color_bgr, bg_color_bgr)))
            
            if distance > distance_threshold:
                # 距离越远，权重越高（使用1.5次幂让权重增长适中）
                weight = (distance ** 1.5) * count
                valid_colors.append((color_bgr, count, distance, weight))
                total_weight += weight
        
        if not valid_colors:
            # 如果没有有效颜色，回退到普通平均色
            avg_color = np.mean(pixels, axis=0).astype(int)
            return self._bgr_to_rgb(avg_color)
        
        # 加权平均计算
        weighted_sum_b = sum(color[0] * weight for color, _, _, weight in valid_colors)
        weighted_sum_g = sum(color[1] * weight for color, _, _, weight in valid_colors)
        weighted_sum_r = sum(color[2] * weight for color, _, _, weight in valid_colors)
        
        final_color_bgr = (
            int(weighted_sum_b / total_weight),
            int(weighted_sum_g / total_weight),
            int(weighted_sum_r / total_weight)
        )
        
        return self._bgr_to_rgb(final_color_bgr)
    
    def extract_text_foreground_color(self, text_region: np.ndarray) -> tuple:
        """
        优化后的颜色提取方法：基于直接二值化 + 主色提取
        
        Args:
            text_region: 文字区域图像（BGR）

        Returns:
            tuple: (text_color, bg_color, is_dark_text)
        """
        try:
            if text_region is None or text_region.size == 0:
                return (0, 0, 0), (255, 255, 255), True

            # 1. 灰度化
            gray = cv2.cvtColor(text_region, cv2.COLOR_BGR2GRAY)
            # 2. 轻度高斯模糊降噪（保留细节）
            blur = cv2.GaussianBlur(gray, (3, 3), 0)
            # 3. 直接OTSU二值化（不做形态学清理）
            _, binary = cv2.threshold(blur, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # 4. 通过边缘采样确定背景，然后判断文字和背景
            white_pixels = np.sum(binary == 255)
            black_pixels = np.sum(binary == 0)
            
            # 采样边缘像素来确定背景色
            edge_binary_values = self._sample_edge_pixels(binary)
            
            if len(edge_binary_values) > 0:
                # 统计边缘像素的二值化结果，占多数的就是背景
                edge_white_count = np.sum(edge_binary_values == 255)
                edge_black_count = np.sum(edge_binary_values == 0)
                
                if edge_white_count > edge_black_count:
                    # 边缘主要是白色，说明白色是背景，黑色是文字
                    text_mask = binary == 0
                    bg_mask = binary == 255
                else:
                    # 边缘主要是黑色，说明黑色是背景，白色是文字
                    text_mask = binary == 255
                    bg_mask = binary == 0
            else:
                # 回退到原来的简单判断
                if white_pixels < black_pixels:
                    text_mask = binary == 255
                    bg_mask = binary == 0
                else:
                    text_mask = binary == 0
                    bg_mask = binary == 255
            
            # 5. 用掩码提取原图对应区域的颜色
            text_pixels = text_region[text_mask]
            bg_pixels = text_region[bg_mask]
            
            # 6. 先计算背景色（不需要排除任何颜色，不打印调试信息）
            bg_color = self._get_dominant_color(bg_pixels, debug_print=False) if len(bg_pixels) > 0 else (255, 255, 255)
            
            # 7. 计算文字色（智能排除背景色，不打印调试信息）
            text_color = self._get_dominant_color(text_pixels, bg_color, debug_print=False) if len(text_pixels) > 0 else (0, 0, 0)
            
            # 8. 判断深色/浅色文字
            text_brightness = np.mean(text_color)
            bg_brightness = np.mean(bg_color)
            is_dark_text = text_brightness < bg_brightness
            
            return text_color, bg_color, is_dark_text
        except Exception as e:
            print(f"颜色提取失败: {e}")
            return (0, 0, 0), (255, 255, 255), True
    
    # ===== 修改Gemini分析方法，移除颜色识别 =====
    
    def create_gemini_analysis_prompt(self, chinese_regions: List, other_regions: List) -> str:
        """创建Gemini分析提示词 - 简化流程"""
        
        # 构建紧凑的OCR数据格式
        ocr_lines = ["OCR格式: ID|文字|语言|x,y,w,h"]
        region_id = 0
        
        # 添加中文区域
        for region in chinese_regions:
            bbox_str = f"{region.bbox[0]},{region.bbox[1]},{region.bbox[2]},{region.bbox[3]}"
            ocr_lines.append(f"{region_id}|{region.text}|zh|{bbox_str}")
            region_id += 1
        
        # 添加其他语言区域
        for region in other_regions:
            bbox_str = f"{region.bbox[0]},{region.bbox[1]},{region.bbox[2]},{region.bbox[3]}"
            ocr_lines.append(f"{region_id}|{region.text}|en|{bbox_str}")
            region_id += 1
        
        ocr_data = "\n".join(ocr_lines)
        
        prompt = f"""分析图片文字布局和翻译需求。

{ocr_data}

简化流程:
1. 文本筛选: 根据原图判断哪些文字需要翻译(产品说明/功效等),哪些不需要(装饰文字/品牌名/产品主体文字等),并说明原因
2. 布局分析: 仅对需要翻译的文本进行对齐分析和分组
3. 翻译处理: 将筛选后的中文翻译成日文,考虑空间约束和重叠风险
4. 样式识别: 识别文字字体和粗细
5. 最大可用区域: 分析每个文本向外延伸的最大安全区域
6. 样式组分组: 将视觉上一致的文本分组(颜色、字号、字体、位置相关)

返回格式:
FILTER:需要翻译:ID1,ID2,ID3;不需要翻译:ID4|品牌名,ID5|装饰文字,ID6|产品主体
LAYOUT:模式|主对齐|左组:文字1,文字2;文字3|中组:文字4|右组:|分布组:
TRANSLATE:ID|原文|日文|适合|重叠
STYLE:ID|字体|粗细
MAXAREA:ID|left_expand,top_expand,right_expand,bottom_expand
STYLEGROUP:组ID|ID1,ID2,ID3|样式描述

说明:
- FILTER: 筛选结果,需要翻译的ID列表;不需要翻译的ID和原因
- LAYOUT: 模式(0=simple 1=horizontal 2=vertical 3=grid 4=complex),主对齐(0=left 1=center 2=right 3=mixed)
- TRANSLATE: 翻译结果,适合/重叠(0=否 1=是)
- STYLE: 字体(Source Han Sans/Noto Sans/PingFang等),粗细(0=normal 1=bold)
- MAXAREA: 最大可用区域,从原OCR文本框向四个方向延伸的像素数(不会遮挡其他元素)
- STYLEGROUP: 样式组,将视觉上一致的文本分组便于统一处理

重要翻译策略:
1. 空间评估: 根据原文区域宽度和相邻文本位置,评估译文是否会超出边界或遮挡其他元素
2. 翻译质量: 保持翻译的准确性和完整性,不进行换行或缩略处理
3. 优先级: 保持意思完整性 > 避免重叠遮挡 > 视觉美观

注意：程序会自动对需要翻译的文本区域进行擦除,不需要翻译的区域保持原样。颜色和字号由程序自动识别。

只返回上述格式,无其他内容。"""

        return prompt
    
    def analyze_with_gemini(self, image_path: str, chinese_regions: List, other_regions: List) -> Dict[str, Any]:
        """使用Gemini分析布局和翻译需求"""
        print(f"\n🤖 正在请求{self.model}分析图片...")
        
        # 编码图片
        base64_image = self.encode_image_to_base64(image_path)
        
        # 创建提示词
        prompt = self.create_gemini_analysis_prompt(chinese_regions, other_regions)
        
        # 构建请求
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 16000,
            "temperature": 0.1
        }
        
        try:
            response = requests.post(self.api_url, headers=headers, json=payload, timeout=60)
            response.raise_for_status()
            
            result = response.json()
            content = result['choices'][0]['message']['content']
            
            # 打印详细的token使用情况
            usage = result.get('usage', {})
            print(f"✅ {self.model}响应成功")
            print(f"📊 Token使用情况:")
            print(f"  输入Token: {usage.get('prompt_tokens', '未知')}")
            print(f"  输出Token: {usage.get('completion_tokens', '未知')}")
            print(f"  总Token: {usage.get('total_tokens', '未知')}")
            print(f"响应内容: {content[:200]}...")
            
            # 打印Gemini原始返回的完整信息
            print(f"\n📝 Gemini原始返回信息:")
            print(f"{'='*60}")
            print(content)
            print(f"{'='*60}")
            print(f"📏 返回内容长度: {len(content)} 字符")
            
            # 简单估算token数量（1个中文字符约等于2个token）
            estimated_tokens = len(content.encode('utf-8')) // 3
            print(f"🔢 估算返回Token数: {estimated_tokens}")
            
            # 解析紧凑格式响应
            try:
                analysis_result = self._parse_compact_response(content)
                print(f"✅ 紧凑格式解析成功")
                return analysis_result
                    
            except Exception as e:
                print(f"❌ 紧凑格式解析失败: {e}")
                print(f"原始响应: {content}")
                return {
                    "layout_analysis": {"layout_mode": "unknown", "main_alignment": "unknown", "alignment_groups": {}},
                    "translation_analysis": {"translation_results": [], "no_translation": []},
                    "confidence": 0.0,
                    "summary": f"解析失败: {e}"
                }
            
        except requests.RequestException as e:
            print(f"❌ API请求失败: {e}")
            return {
                "layout_analysis": {"layout_mode": "unknown", "main_alignment": "unknown", "alignment_groups": {}},
                "translation_analysis": {"translation_results": [], "no_translation": []},
                "confidence": 0.0,
                "summary": f"API请求失败: {e}"
            }
    
    def _parse_compact_response(self, content: str) -> Dict[str, Any]:
        """解析简化格式的Gemini响应"""
        lines = content.strip().split('\n')
        result = {
            "layout_analysis": {
                "layout_mode": "unknown",
                "main_alignment": "unknown", 
                "alignment_groups": {
                    "left_groups": [],
                    "center_groups": [],
                    "right_groups": [],
                    "distribution_groups": []
                }
            },
            "translation_analysis": {
                "translation_results": [],
                "no_translation": []
            },
            "style_analysis": {},
            "max_area_analysis": {},  # 最大可用区域分析
            "style_group_analysis": {},  # 样式组分析
            "confidence": 0.9,
            "summary": "简化格式解析完成"
        }
        
        # 映射数字到字符串
        layout_modes = ["simple", "horizontal", "vertical", "grid", "complex"]
        alignments = ["left", "center", "right", "mixed"]
        
        # 解析结果
        translate_ids = set()
        
        for line in lines:
            line = line.strip()
            
            if line.startswith('FILTER:'):
                # 解析筛选结果: FILTER:需要翻译:ID1,ID2,ID3;不需要翻译:ID4|品牌名,ID5|装饰文字,ID6|产品主体
                filter_content = line[7:].strip()
                if '需要翻译:' in filter_content:
                    # 分割需要翻译和不需要翻译的部分
                    parts = filter_content.split(';不需要翻译:')
                    
                    # 解析需要翻译的ID
                    need_translate_part = parts[0].replace('需要翻译:', '').strip()
                    if need_translate_part:
                        raw_ids = need_translate_part.split(',')
                        # 处理ID格式，去掉可能的"ID"前缀
                        translate_ids = set()
                        for id_str in raw_ids:
                            id_str = id_str.strip()
                            if id_str.startswith('ID'):
                                id_str = id_str[2:]  # 去掉"ID"前缀
                            translate_ids.add(id_str)
                        print(f"📋 Gemini筛选需要翻译的文本ID: {translate_ids}")
                    
                    # 解析不需要翻译的ID和原因
                    if len(parts) > 1:
                        no_translate_part = parts[1].strip()
                        if no_translate_part:
                            no_translate_items = no_translate_part.split(',')
                            for item in no_translate_items:
                                if '|' in item:
                                    region_id, reason = item.split('|', 1)
                                    skip_item = {
                                        "region_id": int(region_id.strip()),
                                        "original_text": f"ID{region_id.strip()}",
                                        "reason": reason.strip()
                                    }
                                    result["translation_analysis"]["no_translation"].append(skip_item)
            
            elif line.startswith('LAYOUT:'):
                # 解析布局信息: LAYOUT:模式|主对齐|左组:文字1,文字2;文字3|中组:文字4|右组:|分布组:
                parts = line[7:].split('|')
                if len(parts) >= 6:
                    # 解析模式和对齐
                    mode_idx = int(parts[0]) if parts[0].isdigit() and int(parts[0]) < len(layout_modes) else 0
                    align_idx = int(parts[1]) if parts[1].isdigit() and int(parts[1]) < len(alignments) else 0
                    
                    result["layout_analysis"]["layout_mode"] = layout_modes[mode_idx]
                    result["layout_analysis"]["main_alignment"] = alignments[align_idx]
                    
                    # 解析分组（只包含需要翻译的文本）
                    group_types = ["left_groups", "center_groups", "right_groups", "distribution_groups"]
                    for i, group_type in enumerate(group_types):
                        if i + 2 < len(parts):
                            group_data = parts[i + 2]
                            if ':' in group_data:
                                group_content = group_data.split(':', 1)[1]
                                if group_content:
                                    # 分号分隔不同组，逗号分隔组内文字
                                    groups = []
                                    for group_str in group_content.split(';'):
                                        if group_str.strip():
                                            group = []
                                            for text in group_str.split(','):
                                                text = text.strip()
                                                if text:
                                                    # 处理ID格式，去掉可能的"ID"前缀
                                                    if text.startswith('ID'):
                                                        text = text[2:]
                                                    group.append(text)
                                            if group:
                                                groups.append(group)
                                    result["layout_analysis"]["alignment_groups"][group_type] = groups
            
            elif line.startswith('TRANSLATE:'):
                # 解析翻译信息: TRANSLATE:ID|原文|日文|适合|重叠
                parts = line[10:].split('|')
                if len(parts) >= 5:
                    try:
                        region_id = int(parts[0])
                        
                        # 只处理需要翻译的文本ID
                        if str(region_id) not in translate_ids:
                            print(f"⏭️ 跳过非翻译文本ID: {region_id}")
                            continue
                        
                        original_text = parts[1]
                        japanese_text = parts[2]
                        
                        # 颜色信息将由程序识别，这里使用默认值
                        color = [0, 0, 0]  # 默认黑色，后续会被程序识别的颜色替换
                        
                        translation_fits = parts[3] == "1"
                        overlap_risk = parts[4] == "0"  # 注意：这里0表示无重叠风险
                        
                        translation_item = {
                            "region_id": region_id,
                            "original_text": original_text,
                            "japanese_text": japanese_text,
                            "color": color,
                            "font_weight": "normal",  # 默认值，将从STYLE行获取
                            "text_align": "center",  # 默认居中，将从布局分析获取
                            "bbox": None,  # 将从OCR数据获取
                            "should_erase": True,  # 需要翻译的文本都需要擦除
                            "space_analysis": {
                                "available_width": None,  # 将从OCR数据计算
                                "translation_fits": translation_fits,
                                "overlap_risk": not overlap_risk
                            }
                        }
                        
                        result["translation_analysis"]["translation_results"].append(translation_item)
                        
                    except (ValueError, IndexError) as e:
                        print(f"⚠️ 解析TRANSLATE行失败: {line}, 错误: {e}")

            
            elif line.startswith('STYLE:'):
                # 解析样式信息: STYLE:ID|字体|粗细
                parts = line[6:].split('|')
                if len(parts) >= 3:
                    try:
                        region_id = int(parts[0])
                        font_family = parts[1]
                        font_weight = "bold" if parts[2] == "1" else "normal"
                        
                        result["style_analysis"][region_id] = {
                            "font_family": font_family,
                            "font_weight": font_weight
                        }
                        
                    except (ValueError, IndexError) as e:
                        print(f"⚠️ 解析STYLE行失败: {line}, 错误: {e}")
            
            elif line.startswith('MAXAREA:'):
                # 解析最大可用区域: MAXAREA:ID|left_expand,top_expand,right_expand,bottom_expand
                parts = line[8:].split('|')
                if len(parts) >= 2:
                    try:
                        region_id = int(parts[0])
                        expand_values = [int(x) for x in parts[1].split(',')]
                        if len(expand_values) == 4:
                            result["max_area_analysis"][region_id] = {
                                "left_expand": expand_values[0],
                                "top_expand": expand_values[1],
                                "right_expand": expand_values[2],
                                "bottom_expand": expand_values[3]
                            }
                        
                    except (ValueError, IndexError) as e:
                        print(f"⚠️ 解析MAXAREA行失败: {line}, 错误: {e}")
            
            elif line.startswith('STYLEGROUP:'):
                # 解析样式组: STYLEGROUP:组ID|ID1,ID2,ID3|样式描述
                parts = line[11:].split('|')
                if len(parts) >= 3:
                    try:
                        group_id = int(parts[0])
                        member_ids = [int(x.strip()) for x in parts[1].split(',') if x.strip()]
                        style_description = parts[2]
                        
                        result["style_group_analysis"][group_id] = {
                            "member_ids": member_ids,
                            "style_description": style_description
                        }
                        
                    except (ValueError, IndexError) as e:
                        print(f"⚠️ 解析STYLEGROUP行失败: {line}, 错误: {e}")
        
        # 将样式信息和最大可用区域信息合并到翻译结果中
        for translation_item in result["translation_analysis"]["translation_results"]:
            region_id = translation_item["region_id"]
            
            # 合并样式信息
            if region_id in result["style_analysis"]:
                style_info = result["style_analysis"][region_id]
                translation_item["font_family"] = style_info["font_family"]
                translation_item["font_weight"] = style_info["font_weight"]
            
            # 合并最大可用区域信息
            if region_id in result["max_area_analysis"]:
                max_area_info = result["max_area_analysis"][region_id]
                translation_item["max_area"] = max_area_info
        
        # 根据布局分析结果设置文本对齐方式
        self._apply_layout_alignment_to_translations(result)
        
        return result
    
    def _apply_layout_alignment_to_translations(self, result: Dict[str, Any]) -> None:
        """根据布局分析结果设置文本对齐方式"""
        layout_analysis = result.get("layout_analysis", {})
        alignment_groups = layout_analysis.get("alignment_groups", {})
        translation_results = result.get("translation_analysis", {}).get("translation_results", [])
        
        if not translation_results:
            return
        
        # 创建ID到对齐方式的映射
        id_to_alignment = {}
        
        # 处理各种对齐组
        alignment_mappings = {
            "left_groups": "left",
            "center_groups": "center", 
            "right_groups": "right",
            "distribution_groups": "center"  # 分布组默认居中
        }
        
        for group_type, alignment in alignment_mappings.items():
            groups = alignment_groups.get(group_type, [])
            for group in groups:
                for region_id in group:
                    # 处理ID格式，确保是整数
                    if isinstance(region_id, str):
                        if region_id.startswith('ID'):
                            region_id = region_id[2:]
                        try:
                            region_id = int(region_id)
                        except ValueError:
                            continue
                    id_to_alignment[region_id] = alignment
        
        # 应用对齐方式到翻译结果
        for translation_item in translation_results:
            region_id = translation_item.get("region_id")
            if region_id in id_to_alignment:
                old_align = translation_item.get("text_align", "center")
                new_align = id_to_alignment[region_id]
                translation_item["text_align"] = new_align
                print(f"    🎯 设置对齐方式: ID{region_id} '{translation_item.get('original_text', '')}' {old_align} → {new_align}")
            else:
                print(f"    ⚠️ 未找到对齐信息: ID{region_id} '{translation_item.get('original_text', '')}' 使用默认居中")
    
    def execute_smart_translation(self, image_path: str, gemini_analysis: Dict[str, Any],
                                 chinese_regions: List, color_analysis: Dict[str, Any] = None) -> None:
        """执行智能翻译流程 - 新的基于Gemini判断的流程"""
        # 保存gemini_analysis到实例变量供其他方法使用
        self._current_gemini_analysis = gemini_analysis
        
        translation_analysis = gemini_analysis.get("translation_analysis", {})
        translation_results = translation_analysis.get("translation_results", [])

        if not translation_results:
            print("⚠️ Gemini分析结果显示无需翻译的文字")
            return

        print(f"\n🔥 开始智能翻译流程...")
        print(f"Gemini识别需要翻译的文字: {len(translation_results)} 个")

        try:
            # 1. 导入处理器
            from processors.inpaint_processor import InpaintProcessor
            from processors.renderer import Renderer
            from processors.font_processor import FontProcessor
            from models.data_models import TranslationResult, FontMatchResult, StyleInfo, LayoutResult

            # 2. 加载原始图像
            original_image = cv2.imread(image_path)
            if original_image is None:
                print(f"❌ 无法加载图片: {image_path}")
                return

            # 3. 根据Gemini筛选结果确定需要擦除的区域（需要翻译的文本都需要擦除）
            regions_to_erase = []
            translation_items_to_process = []

            for translation_item in translation_results:
                region_id = translation_item.get("region_id", -1)
                original_text = translation_item.get("original_text", "")

                # 找到对应的OCR区域（支持Gemini合并文字的情况）
                matching_regions = []

                # 首先尝试精确匹配
                for region in chinese_regions:
                    if region.text == original_text:
                        matching_regions.append(region)
                        break

                # 如果没有精确匹配，尝试部分匹配（Gemini可能合并了多个OCR区域）
                if not matching_regions:
                    # 将original_text按空格或标点分割，查找包含的OCR区域
                    import re
                    text_parts = re.split(r'[，。、\s]+', original_text)
                    text_parts = [part.strip() for part in text_parts if part.strip()]

                    for part in text_parts:
                        for region in chinese_regions:
                            if part in region.text or region.text in part:
                                if region not in matching_regions:
                                    matching_regions.append(region)

                if matching_regions:
                    regions_to_erase.extend(matching_regions)
                    translation_items_to_process.append(translation_item)
                    if len(matching_regions) == 1:
                        print(f"✅ 找到匹配区域: '{original_text}' -> 需要翻译和擦除")
                    else:
                        region_texts = [r.text for r in matching_regions]
                        print(f"✅ 找到合并匹配区域: '{original_text}' -> {region_texts} -> 需要翻译和擦除")
                else:
                    print(f"⚠️ 未找到匹配的OCR区域: '{original_text}'")

            print(f"最终需要擦除的区域: {len(regions_to_erase)} 个（所有需要翻译的文本都会被擦除）")
            
            # 4. 如果没有需要擦除的区域，直接返回
            if not regions_to_erase:
                print("⚠️ 没有需要擦除的区域，跳过后续处理")
                return

            # 5. 初始化处理器
            inpaint_processor = InpaintProcessor()
            renderer = Renderer(weight_adjustment=400)
            font_processor = FontProcessor()

            # 6. 执行LAMA擦除（只擦除Gemini判断需要擦除的区域）
            print(f"🎯 使用LAMA擦除 {len(regions_to_erase)} 个Gemini判断需要擦除的区域...")
            inpaint_result = inpaint_processor.process_inpainting(original_image, regions_to_erase)

            if not inpaint_result.success:
                print(f"❌ LAMA擦除失败: {inpaint_result.error_message}")
                return

            inpainted_image = inpaint_result.data
            print("✅ LAMA擦除完成")

            # 7. 使用Gemini识别的字体信息（不再执行程序的字体匹配）
            print("使用Gemini识别的字体信息...")
            font_matches = self._create_font_matches_from_gemini(translation_items_to_process)

            # 8. 构建翻译对象（使用Gemini的翻译结果和程序识别的颜色）
            print("构建翻译对象...")
            
            # 8.1 计算样式组缩放比例
            style_group_scaling = self._calculate_style_group_scaling(gemini_analysis, chinese_regions)
            
            translation_objects = self._build_smart_translation_objects(
                translation_items_to_process, regions_to_erase, font_matches, color_analysis, style_group_scaling
            )
            
            # 9. 创建布局结果（基于Gemini的布局分析）
            layout_analysis = gemini_analysis.get("layout_analysis", {})
            alignment_groups = layout_analysis.get("alignment_groups", {})

            # 构建兼容的水平对齐结构
            horizontal_alignment = {
                "type": layout_analysis.get("main_alignment", "center"),
                "left_groups": alignment_groups.get("left_groups", []),
                "center_groups": alignment_groups.get("center_groups", []),
                "right_groups": alignment_groups.get("right_groups", []),
                "distribution_groups": alignment_groups.get("distribution_groups", [])
            }

            layout_result = LayoutResult(
                layout_mode=layout_analysis.get("layout_mode", "simple"),
                regions=[],  # 这里传空列表，因为我们使用Gemini的分析结果
                horizontal_alignment=horizontal_alignment,
                vertical_distribution={"type": "simple"},
                alignment_strategies=[]
            )

            # 10. 渲染翻译文字（基于Gemini的翻译结果）
            print(f"🎨 渲染 {len(translation_objects)} 个日文文本...")
            render_result = renderer.render_translations(
                inpainted_image, translation_objects, layout_result, image_path
            )

            if not render_result.success:
                print(f"❌ 文本渲染失败: {render_result.error_message}")
                return

            final_image = render_result.data['image']
            render_log = render_result.data['render_log']

            # 11. 保存结果
            output_path = os.path.join(self.output_dir, "smart_translated.png")
            cv2.imwrite(output_path, final_image)
            print(f"✅ 智能翻译结果已保存: {output_path}")

            # 12. 保存翻译对比图
            self._save_comparison_image(original_image, final_image, "smart_translation_comparison.png")

            # 14. 输出渲染日志
            print(f"\n📝 渲染日志:")
            for i, log in enumerate(render_log, 1):
                print(f"  {i}. '{log['original']}' → '{log['translated']}' "
                      f"at ({log['position'][0]}, {log['position'][1]}) "
                      f"size {log['size']}px")

            print(f"\n🎉 智能翻译流程完成！处理了 {len(render_log)} 个文本")

            # 15. 输出Gemini分析摘要
            print(f"\n📊 Gemini简化分析摘要:")
            translation_analysis = gemini_analysis.get("translation_analysis", {})
            no_translation = translation_analysis.get("no_translation", [])
            print(f"  - 需要翻译并擦除: {len(translation_objects)} 个")
            print(f"  - 不需要翻译（保持原样）: {len(no_translation)} 个")
            print(f"  - 实际擦除区域: {len(regions_to_erase)} 个")
            print(f"  - 置信度: {gemini_analysis.get('confidence', 0.0):.2f}")

            # 16. 输出字号使用摘要
            print(f"\n📏 字号使用摘要:")
            for translation_obj in translation_objects:
                ocr_height = translation_obj.style_info.precise_height
                print(f"  '{translation_obj.original_text}': OCR统一高度={ocr_height}px")
            
        except Exception as e:
            print(f"❌ 智能翻译流程失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _build_smart_translation_objects(self, translation_items: List[Dict],
                                        regions_to_erase: List, font_matches: List, color_analysis: Dict[str, Any] = None,
                                        style_group_scaling: Dict[int, float] = None) -> List:
        """构建智能翻译对象 - 基于Gemini的翻译结果"""
        from models.data_models import TranslationResult, StyleInfo

        translation_objects = []
        
        # 创建ID到样式组的映射
        id_to_style_group = {}
        if style_group_scaling:
            # 从Gemini分析结果获取样式组信息
            style_group_analysis = getattr(self, '_current_gemini_analysis', {}).get("style_group_analysis", {})
            for group_id, group_info in style_group_analysis.items():
                member_ids = group_info.get("member_ids", [])
                for member_id in member_ids:
                    id_to_style_group[member_id] = group_id

        for translation_item in translation_items:
            original_text = translation_item.get("original_text", "")
            japanese_text = translation_item.get("japanese_text", "")
            font_weight = translation_item.get("font_weight", "normal")
            bbox = translation_item.get("bbox", None)
            
            # 使用程序识别的颜色，如果没有则使用默认颜色
            if color_analysis and original_text in color_analysis:
                color = color_analysis[original_text]['text_color']
                print(f"    🎨 使用程序识别的颜色: '{original_text}' -> {color}")
            else:
                color = [0, 0, 0]  # 默认黑色
                print(f"    ⚠️ 使用默认颜色: '{original_text}' -> {color}")

            # 查找对应的OCR区域（bbox信息从OCR数据获取）
            matching_region = None
            for region in regions_to_erase:
                if region.text == original_text or original_text in region.text:
                    matching_region = region
                    break

            if not matching_region:
                print(f"⚠️ 未找到匹配的OCR区域: {original_text}")
                continue

            matching_region_bbox = matching_region.bbox

            # 找到对应的字体匹配
            matching_font = None
            for font_match in font_matches:
                if font_match.text == original_text or original_text in font_match.text:
                    matching_font = font_match
                    break

            if not matching_font:
                print(f"⚠️ 未找到匹配的字体: {original_text}，使用默认字体")
                from models.data_models import FontMatchResult
                matching_font = FontMatchResult(
                    text=original_text,
                    matched_font="NotoSansSC",
                    font_path=os.path.join(os.path.dirname(os.path.dirname(__file__)), "fonts/NotoSansSC/NotoSansSC-Black.ttf"),
                    confidence=0.8,
                    supports_japanese=True,
                    region_id=0
                )

            # 获取Gemini的空间分析和对齐信息
            space_analysis = translation_item.get("space_analysis", {})
            alignment_analysis = translation_item.get("alignment_analysis", {})
            text_align = alignment_analysis.get("detected_alignment", translation_item.get("text_align", "left"))

            # 输出空间分析信息
            if space_analysis:
                available_width = space_analysis.get("available_width", "未知")
                translation_fits = space_analysis.get("translation_fits", True)
                overlap_risk = space_analysis.get("overlap_risk", False)
                length_optimization = space_analysis.get("length_optimization", "")
                overlap_solution = space_analysis.get("overlap_solution", "")

                fit_status = "✅适合" if translation_fits else "⚠️可能过长"
                overlap_status = "⚠️有重叠风险" if overlap_risk else "✅无重叠"
                print(f"    空间分析: 可用宽度{available_width}px, {fit_status}, {overlap_status}")
                if length_optimization:
                    print(f"    优化策略: {length_optimization}")
                if overlap_solution:
                    print(f"    重叠解决: {overlap_solution}")

            # 输出对齐分析信息
            if alignment_analysis:
                detected_alignment = alignment_analysis.get("detected_alignment", "未知")
                alignment_confidence = alignment_analysis.get("alignment_confidence", 0.0)
                alignment_reason = alignment_analysis.get("alignment_reason", "")

                print(f"    对齐分析: {detected_alignment} (置信度:{alignment_confidence:.2f})")
                if alignment_reason:
                    print(f"    对齐原因: {alignment_reason}")

            # 直接使用OCR阶段统一的像素级高度作为字号
            # 优先使用经过像素级统一处理的precise_height，如果没有则使用bbox高度
            if matching_region.style_info and 'precise_height' in matching_region.style_info:
                ocr_unified_height = matching_region.style_info['precise_height']
                print(f"    使用OCR统一高度: '{original_text}' -> {ocr_unified_height}px")
            else:
                ocr_unified_height = matching_region_bbox[3]  # 使用原图文字高度作为后备
                print(f"    使用bbox高度: '{original_text}' -> {ocr_unified_height}px")

            # 应用样式组缩放比例
            region_id = translation_item.get("region_id")
            group_scale_factor = 1.0
            if region_id in id_to_style_group:
                group_id = id_to_style_group[region_id]
                if group_id in style_group_scaling:
                    group_scale_factor = style_group_scaling[group_id]
                    print(f"    应用样式组缩放: ID{region_id} 样式组{group_id} 缩放比例{group_scale_factor:.2f}")
            
            # 计算最终字号（应用样式组缩放）
            final_font_size = int(ocr_unified_height * group_scale_factor)
            
            # 构建样式信息（使用应用样式组缩放后的字号）
            style_info = StyleInfo(
                estimated_font_size=final_font_size,
                precise_height=final_font_size,  # 使用应用样式组缩放后的字号
                color=tuple(color) if isinstance(color, list) else color,
                background_color=(255, 255, 255),
                is_bold=(font_weight == "bold"),
                is_dark_text=True,
                contrast_ratio=4.5
            )

            # 创建翻译对象
            translation_obj = TranslationResult(
                original_text=original_text,
                translated_text=japanese_text,
                bbox=matching_region_bbox,
                style_info=style_info,
                font_info=matching_font,
                group_key=f"gemini_group_{len(translation_objects)}",
                group_scale_factor=group_scale_factor,
                final_font_size=final_font_size  # 使用应用样式组缩放后的字号
            )

            # 添加对齐信息到翻译对象（用于渲染时参考）
            translation_obj.text_align = text_align

            translation_objects.append(translation_obj)

            # 输出翻译对象构建信息
            print(f"✅ 构建翻译对象: '{original_text}' -> '{japanese_text}'")
            print(f"    OCR统一高度: {ocr_unified_height}px")
            if group_scale_factor != 1.0:
                print(f"    样式组缩放: {group_scale_factor:.2f} -> 最终字号: {final_font_size}px")
            else:
                print(f"    无需缩放 -> 最终字号: {final_font_size}px")

        return translation_objects



    def _calculate_style_group_scaling(self, gemini_analysis: Dict[str, Any], chinese_regions: List) -> Dict[int, float]:
        """计算样式组的统一缩放比例"""
        style_group_analysis = gemini_analysis.get("style_group_analysis", {})
        translation_results = gemini_analysis.get("translation_analysis", {}).get("translation_results", [])
        
        if not style_group_analysis:
            print("⚠️ 没有样式组信息，跳过样式组缩放计算")
            return {}
        
        # 创建OCR区域映射
        ocr_regions_map = {}
        for region in chinese_regions:
            ocr_regions_map[region.text] = region.bbox
        
        # 创建翻译结果映射
        translation_map = {}
        for item in translation_results:
            region_id = item.get("region_id")
            if region_id is not None:
                translation_map[region_id] = item
        
        group_scaling = {}
        
        print(f"\n🔄 计算样式组缩放比例...")
        
        for group_id, group_info in style_group_analysis.items():
            member_ids = group_info.get("member_ids", [])
            style_description = group_info.get("style_description", "")
            
            print(f"\n📊 样式组 {group_id}: {style_description}")
            print(f"   成员: {member_ids}")
            
            # 计算每个成员的缩放需求
            scaling_ratios = []
            
            for member_id in member_ids:
                if member_id not in translation_map:
                    continue
                
                translation_item = translation_map[member_id]
                original_text = translation_item.get("original_text", "")
                japanese_text = translation_item.get("japanese_text", "")
                max_area = translation_item.get("max_area", {})
                
                # 获取原始OCR区域
                bbox = None
                if original_text in ocr_regions_map:
                    bbox = ocr_regions_map[original_text]
                
                if bbox is None or not max_area:
                    print(f"   ⚠️ 成员 {member_id} 缺少区域信息，跳过")
                    continue
                
                # 计算最大可用区域
                left_expand = max_area.get("left_expand", 0)
                right_expand = max_area.get("right_expand", 0)
                max_width = bbox[2] + left_expand + right_expand
                
                # 估算日文文本宽度（修正估算：日文字符数 * 字号 * 1.0，更接近实际宽度）
                font_size = bbox[3]  # 使用原始高度作为字号
                estimated_width = len(japanese_text) * font_size * 1.0
                
                # 计算缩放比例
                if estimated_width > max_width:
                    scaling_ratio = max_width / estimated_width
                    scaling_ratios.append(scaling_ratio)
                    print(f"   成员 {member_id} '{original_text}': 需要缩放 {scaling_ratio:.2f} (估算宽度:{estimated_width:.0f}px > 最大宽度:{max_width}px)")
                else:
                    print(f"   成员 {member_id} '{original_text}': 无需缩放 (估算宽度:{estimated_width:.0f}px <= 最大宽度:{max_width}px)")
            
            # 取最小缩放比例作为整个样式组的缩放比例
            if scaling_ratios:
                group_scaling_ratio = min(scaling_ratios)
                group_scaling[group_id] = group_scaling_ratio
                print(f"   ✅ 样式组 {group_id} 统一缩放比例: {group_scaling_ratio:.2f}")
            else:
                group_scaling[group_id] = 1.0
                print(f"   ✅ 样式组 {group_id} 无需缩放")
        
        return group_scaling

    def _create_font_matches_from_gemini(self, translation_items: List[Dict]) -> List:
        """根据Gemini识别的字体信息创建字体匹配结果"""
        from models.data_models import FontMatchResult

        # 字体映射：Gemini识别的字体名 -> 项目中的字体路径（支持字重选择）
        font_mapping = {
            "Source Han Sans": {
                "normal": "fonts/思源黑体/SourceHanSans-Regular.ttf",
                "bold": "fonts/思源黑体/SourceHanSans-Bold.ttf"
            },
            "Noto Sans": "fonts/NotoSansSC/NotoSansSC-Black.ttf",
            "Noto Sans SC": "fonts/NotoSansSC/NotoSansSC-Black.ttf",
            "思源黑体": {
                "normal": "fonts/思源黑体/SourceHanSans-Regular.ttf",
                "bold": "fonts/思源黑体/SourceHanSans-Bold.ttf"
            },
            "台北黑体": "fonts/台北黑体/TaipeiSans-Bold.ttf",
            "PingFang SC": {
                "normal": "fonts/苹方/PingFang-Regular.ttf",
                "bold": "fonts/苹方/PingFang-Bold.ttf"
            },
            "PingFang": {
                "normal": "fonts/苹方/PingFang-Regular.ttf",
                "bold": "fonts/苹方/PingFang-Bold.ttf"
            },
            "苹方": {
                "normal": "fonts/苹方/PingFang-Regular.ttf",
                "bold": "fonts/苹方/PingFang-Bold.ttf"
            }
        }

        default_font_path = "fonts/NotoSansSC/NotoSansSC-Black.ttf"
        font_matches = []

        for i, translation_item in enumerate(translation_items):
            original_text = translation_item.get("original_text", "")
            gemini_font = translation_item.get("font_family", "Source Han Sans")
            font_weight = translation_item.get("font_weight", "normal")

            # 查找对应的字体路径
            font_config = font_mapping.get(gemini_font, default_font_path)
            
            # 处理支持字重选择的字体
            if isinstance(font_config, dict):
                font_path = font_config.get(font_weight, font_config.get("normal", default_font_path))
                print(f"🎯 选择字重: '{original_text}' -> {gemini_font} {font_weight}")
            else:
                font_path = font_config

            # 检查字体文件是否存在
            full_font_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), font_path)
            if not os.path.exists(full_font_path):
                print(f"⚠️ Gemini识别的字体 '{gemini_font}' ({font_weight}) 在项目中不存在，使用默认字体")
                font_path = default_font_path
                full_font_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), font_path)
                matched_font_name = "NotoSansSC"
            else:
                matched_font_name = gemini_font
                print(f"✅ 使用Gemini识别的字体: '{original_text}' -> {gemini_font} ({font_weight})")

            font_match = FontMatchResult(
                text=original_text,
                matched_font=matched_font_name,
                font_path=full_font_path,
                confidence=0.95,  # Gemini识别的置信度较高
                supports_japanese=True,
                region_id=i
            )

            font_matches.append(font_match)

        return font_matches

    def _save_comparison_image(self, original_image: np.ndarray, final_image: np.ndarray,
                              filename: str) -> None:
        """保存对比图"""
        try:
            h, w = original_image.shape[:2]
            comparison = np.zeros((h, w * 2, 3), dtype=np.uint8)
            
            # 左侧：原图
            comparison[:, :w] = original_image
            
            # 右侧：翻译结果
            comparison[:, w:] = final_image
            
            # 添加分割线
            cv2.line(comparison, (w, 0), (w, h), (255, 255, 255), 2)
            
            # 添加标签
            font = cv2.FONT_HERSHEY_SIMPLEX
            cv2.putText(comparison, "Original", (10, 30), font, 1, (255, 255, 255), 2)
            cv2.putText(comparison, "Smart Translated", (w + 10, 30), font, 1, (255, 255, 255), 2)
            
            # 保存对比图
            comparison_path = os.path.join(self.output_dir, filename)
            cv2.imwrite(comparison_path, comparison)
            print(f"✅ 对比图已保存: {comparison_path}")
            
        except Exception as e:
            print(f"⚠️ 保存对比图失败: {e}")
    
    def generate_layout_debug_image(self, image_path: str, gemini_analysis: Dict[str, Any], 
                                   chinese_regions: List, other_regions: List) -> None:
        """生成布局分析调试图"""
        print(f"\n🎨 生成布局分析调试图...")
        
        layout_analysis = gemini_analysis.get("layout_analysis", {})
        alignment_groups = layout_analysis.get("alignment_groups", {})
        
        # 加载原图
        base_image = cv2.imread(image_path)
        if base_image is None:
            print(f"❌ 无法加载图片: {image_path}")
            return
        
        # 创建区域映射
        all_regions = {}
        for region in chinese_regions + other_regions:
            all_regions[region.text] = {
                'bbox': region.bbox,
                'left': region.bbox[0],
                'top': region.bbox[1],
                'right': region.bbox[0] + region.bbox[2],
                'bottom': region.bbox[1] + region.bbox[3],
                'center': [region.bbox[0] + region.bbox[2]//2, region.bbox[1] + region.bbox[3]//2],
                'text': region.text
            }
        
        # 生成对齐分组调试图
        alignment_types = [
            ('left_groups', '左对齐', (0, 255, 0)),
            ('center_groups', '居中对齐', (255, 0, 0)),
            ('right_groups', '右对齐', (0, 0, 255)),
            ('distribution_groups', '分布对齐', (255, 255, 0))
        ]
        
        for group_type, label, color in alignment_types:
            groups = alignment_groups.get(group_type, [])
            if not groups:
                continue
            
            # 创建图片副本
            img_copy = base_image.copy()
            pil_image = Image.fromarray(cv2.cvtColor(img_copy, cv2.COLOR_BGR2RGB))
            overlay = Image.new('RGBA', pil_image.size, (0, 0, 0, 0))
            draw = ImageDraw.Draw(overlay)
            
            # 尝试加载中文字体
            try:
                font_paths = [
                    "/System/Library/Fonts/PingFang.ttc",
                    "/System/Library/Fonts/STHeiti Light.ttc", 
                    "/System/Library/Fonts/Hiragino Sans GB.ttc"
                ]
                
                font_large = font_medium = font_small = None
                for font_path in font_paths:
                    try:
                        font_large = ImageFont.truetype(font_path, 32)
                        font_medium = ImageFont.truetype(font_path, 24)
                        font_small = ImageFont.truetype(font_path, 18)
                        break
                    except:
                        continue
                
                if not font_large:
                    font_large = font_medium = font_small = ImageFont.load_default()
                    
            except Exception as e:
                font_large = font_medium = font_small = ImageFont.load_default()
            
            # 绘制标题
            title = f"Gemini智能分析 - {label} ({len(groups)}组)"
            draw.text((30, 30), title, fill=(40, 40, 40, 255), font=font_large)
            
            # 绘制各组
            for group_idx, group in enumerate(groups):
                group_color = (*color, 200)
                
                for text in group:
                    if text in all_regions:
                        region = all_regions[text]
                        
                        # 绘制区域框
                        draw.rectangle([
                            region['left'], region['top'], 
                            region['right'], region['bottom']
                        ], outline=group_color, width=2)
                        
                        # 绘制半透明填充
                        draw.rectangle([
                            region['left'], region['top'], 
                            region['right'], region['bottom']
                        ], fill=(*color, 30))
                        
                        # 绘制文本标签
                        label_text = f"{text}"
                        text_x = region['left'] + 2
                        text_y = region['top'] - 25 if region['top'] > 25 else region['bottom'] + 2
                        
                        # 文本背景
                        try:
                            bbox = draw.textbbox((text_x, text_y), label_text, font=font_small)
                            draw.rectangle([bbox[0]-2, bbox[1]-2, bbox[2]+2, bbox[3]+2], 
                                         fill=(255, 255, 255, 220))
                        except:
                            pass
                        
                        draw.text((text_x, text_y), label_text, fill=(40, 40, 40, 255), font=font_small)
            
            # 保存调试图
            final_image = Image.alpha_composite(pil_image.convert('RGBA'), overlay)
            final_image = final_image.convert('RGB')
            cv2_image = cv2.cvtColor(np.array(final_image), cv2.COLOR_RGB2BGR)
            
            filename = f"layout_debug_{group_type}.png"
            output_path = os.path.join(self.output_dir, filename)
            cv2.imwrite(output_path, cv2_image)
            print(f"✅ 布局调试图已保存: {filename}")
    
    def generate_display_area_debug_image(self, image_path: str, gemini_analysis: Dict[str, Any], 
                                   chinese_regions: List = None, other_regions: List = None) -> None:
        """生成显示范围调试图 - 显示Gemini预测的文本显示区域"""
        print(f"\n🎯 生成显示范围调试图...")
        
        translation_analysis = gemini_analysis.get("translation_analysis", {})
        translation_results = translation_analysis.get("translation_results", [])
        
        if not translation_results:
            print("⚠️ 没有翻译结果，跳过显示范围调试图生成")
            return
        
        # 加载原图
        base_image = cv2.imread(image_path)
        if base_image is None:
            print(f"❌ 无法加载图片: {image_path}")
            return
        
        # 创建PIL图像用于绘制
        pil_image = Image.fromarray(cv2.cvtColor(base_image, cv2.COLOR_BGR2RGB))
        overlay = Image.new('RGBA', pil_image.size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(overlay)
        
        # 尝试加载中文字体
        try:
            font_paths = [
                "/System/Library/Fonts/PingFang.ttc",
                "/System/Library/Fonts/STHeiti Light.ttc", 
                "/System/Library/Fonts/Hiragino Sans GB.ttc"
            ]
            
            font_large = font_medium = font_small = None
            for font_path in font_paths:
                try:
                    font_large = ImageFont.truetype(font_path, 32)
                    font_medium = ImageFont.truetype(font_path, 24)
                    font_small = ImageFont.truetype(font_path, 18)
                    break
                except:
                    continue
            
            if not font_large:
                font_large = font_medium = font_small = ImageFont.load_default()
                
        except Exception as e:
            font_large = font_medium = font_small = ImageFont.load_default()
        
        # 绘制标题
        title = f"Gemini预测显示范围调试 ({len(translation_results)}个译文)"
        draw.text((30, 30), title, fill=(40, 40, 40, 255), font=font_large)
        
        # 创建OCR区域映射
        ocr_regions_map = {}
        if chinese_regions:
            for region in chinese_regions:
                ocr_regions_map[region.text] = region.bbox
        if other_regions:
            for region in other_regions:
                ocr_regions_map[region.text] = region.bbox

        # 填充translation_results中的bbox信息
        for translation_item in translation_results:
            original_text = translation_item.get("original_text", "")
            
            # 从OCR数据中获取bbox信息
            bbox = None
            if original_text in ocr_regions_map:
                bbox = ocr_regions_map[original_text]
            else:
                # 如果没有精确匹配，尝试部分匹配
                for ocr_text, ocr_bbox in ocr_regions_map.items():
                    if original_text in ocr_text or ocr_text in original_text:
                        bbox = ocr_bbox
                        break
            
            # 更新translation_item中的bbox信息和可用宽度
            if bbox is not None:
                translation_item["bbox"] = bbox
                # 计算可用宽度（使用原始文本区域的宽度）
                available_width = bbox[2]  # bbox[2]是宽度
                space_analysis = translation_item.get("space_analysis", {})
                space_analysis["available_width"] = available_width
                translation_item["space_analysis"] = space_analysis

        # 为每个翻译结果绘制显示范围
        for i, translation_item in enumerate(translation_results):
            original_text = translation_item.get("original_text", "")
            japanese_text = translation_item.get("japanese_text", "")
            
            # 从OCR数据中获取bbox信息
            bbox = None
            if original_text in ocr_regions_map:
                bbox = ocr_regions_map[original_text]
            else:
                # 如果没有精确匹配，尝试部分匹配
                for ocr_text, ocr_bbox in ocr_regions_map.items():
                    if original_text in ocr_text or ocr_text in original_text:
                        bbox = ocr_bbox
                        break
            
            # 如果还是没有找到bbox，跳过这个翻译项
            if bbox is None:
                print(f"⚠️ 未找到'{original_text}'的OCR区域信息，跳过显示")
                continue
            
            # 原始OCR区域（绿色框）
            original_left = bbox[0]
            original_top = bbox[1]
            original_right = bbox[0] + bbox[2]
            original_bottom = bbox[1] + bbox[3]
            
            # 绘制原始OCR区域
            draw.rectangle([
                original_left, original_top, 
                original_right, original_bottom
            ], outline=(0, 255, 0, 200), width=2)
            
            # 绘制原始区域标签
            original_label = f"原文: {original_text}"
            draw.text((original_left + 2, original_top - 25), original_label, 
                     fill=(0, 150, 0, 255), font=font_small)
            
            # 简化处理：不再显示预测显示区域，只显示翻译文本
            # 绘制翻译文本标签
            translated_label = f"译文: {japanese_text}"
            label_y = original_bottom + 5
            draw.text((original_left + 2, label_y), translated_label, 
                     fill=(200, 0, 0, 255), font=font_small)
            
            print(f"✅ 绘制翻译文本: '{original_text}' -> '{japanese_text}'")
        
        # 添加图例
        legend_y = 80
        legend_items = [
            ("原文OCR区域", (0, 255, 0)),
            ("翻译文本", (255, 0, 0))
        ]
        
        for legend_text, legend_color in legend_items:
            # 绘制图例色块
            draw.rectangle([30, legend_y, 50, legend_y + 15], 
                          fill=(*legend_color, 200))
            draw.rectangle([30, legend_y, 50, legend_y + 15], 
                          outline=(*legend_color, 255), width=1)
            
            # 绘制图例文字
            draw.text((60, legend_y), legend_text, fill=(40, 40, 40, 255), font=font_small)
            legend_y += 25
        
        # 保存调试图
        final_image = Image.alpha_composite(pil_image.convert('RGBA'), overlay)
        final_image = final_image.convert('RGB')
        cv2_image = cv2.cvtColor(np.array(final_image), cv2.COLOR_RGB2BGR)
        
        filename = "display_area_debug.png"
        output_path = os.path.join(self.output_dir, filename)
        cv2.imwrite(output_path, cv2_image)
        print(f"✅ 显示范围调试图已保存: {filename}")
    
    def generate_max_area_debug_image(self, image_path: str, gemini_analysis: Dict[str, Any], 
                                   chinese_regions: List = None, other_regions: List = None) -> None:
        """生成最大可用区域调试图 - 显示每个文本的最大可用区域"""
        print(f"\n🎯 生成最大可用区域调试图...")
        
        translation_analysis = gemini_analysis.get("translation_analysis", {})
        translation_results = translation_analysis.get("translation_results", [])
        max_area_analysis = gemini_analysis.get("max_area_analysis", {})
        
        if not translation_results or not max_area_analysis:
            print("⚠️ 没有最大可用区域数据，跳过调试图生成")
            return
        
        # 加载原图
        base_image = cv2.imread(image_path)
        if base_image is None:
            print(f"❌ 无法加载图片: {image_path}")
            return
        
        # 创建PIL图像用于绘制
        pil_image = Image.fromarray(cv2.cvtColor(base_image, cv2.COLOR_BGR2RGB))
        overlay = Image.new('RGBA', pil_image.size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(overlay)
        
        # 尝试加载中文字体
        try:
            font_paths = [
                "/System/Library/Fonts/PingFang.ttc",
                "/System/Library/Fonts/STHeiti Light.ttc", 
                "/System/Library/Fonts/Hiragino Sans GB.ttc"
            ]
            
            font_large = font_medium = font_small = None
            for font_path in font_paths:
                try:
                    font_large = ImageFont.truetype(font_path, 32)
                    font_medium = ImageFont.truetype(font_path, 24)
                    font_small = ImageFont.truetype(font_path, 18)
                    break
                except:
                    continue
            
            if not font_large:
                font_large = font_medium = font_small = ImageFont.load_default()
                
        except Exception as e:
            font_large = font_medium = font_small = ImageFont.load_default()
        
        # 绘制标题
        title = f"最大可用区域调试 ({len(translation_results)}个文本)"
        draw.text((30, 30), title, fill=(40, 40, 40, 255), font=font_large)
        
        # 创建OCR区域映射
        ocr_regions_map = {}
        if chinese_regions:
            for region in chinese_regions:
                ocr_regions_map[region.text] = region.bbox
        if other_regions:
            for region in other_regions:
                ocr_regions_map[region.text] = region.bbox

        # 为每个翻译结果绘制最大可用区域
        colors = [
            (255, 0, 0, 100),    # 红色
            (0, 255, 0, 100),    # 绿色
            (0, 0, 255, 100),    # 蓝色
            (255, 255, 0, 100),  # 黄色
            (255, 0, 255, 100),  # 紫色
            (0, 255, 255, 100),  # 青色
        ]
        
        for i, translation_item in enumerate(translation_results):
            original_text = translation_item.get("original_text", "")
            region_id = translation_item.get("region_id", 0)
            
            # 获取OCR区域
            bbox = None
            if original_text in ocr_regions_map:
                bbox = ocr_regions_map[original_text]
            
            # 获取最大可用区域信息
            max_area_info = max_area_analysis.get(region_id, {})
            
            if bbox is None or not max_area_info:
                print(f"⚠️ 未找到'{original_text}'的区域或最大可用区域信息，跳过")
                continue
            
            # 计算最大可用区域
            left_expand = max_area_info.get("left_expand", 0)
            top_expand = max_area_info.get("top_expand", 0)
            right_expand = max_area_info.get("right_expand", 0)
            bottom_expand = max_area_info.get("bottom_expand", 0)
            
            # 原始OCR区域
            original_left = bbox[0]
            original_top = bbox[1]
            original_right = bbox[0] + bbox[2]
            original_bottom = bbox[1] + bbox[3]
            
            # 最大可用区域
            max_left = original_left - left_expand
            max_top = original_top - top_expand
            max_right = original_right + right_expand
            max_bottom = original_bottom + bottom_expand
            
            # 确保不超出图像边界
            max_left = max(0, max_left)
            max_top = max(0, max_top)
            max_right = min(pil_image.width, max_right)
            max_bottom = min(pil_image.height, max_bottom)
            
            # 绘制最大可用区域（半透明填充）
            color = colors[i % len(colors)]
            draw.rectangle([max_left, max_top, max_right, max_bottom], 
                         fill=color, outline=None)
            
            # 绘制原始OCR区域（实线框）
            draw.rectangle([original_left, original_top, original_right, original_bottom], 
                         outline=(255, 255, 255, 255), width=2)
            
            # 绘制文本标签
            label = f"ID{region_id}: {original_text[:10]}..."
            label_y = max_top - 25 if max_top > 25 else max_bottom + 5
            draw.text((max_left + 2, label_y), label, 
                     fill=(255, 255, 255, 255), font=font_small)
            
            # 绘制扩展信息
            expand_info = f"↑{top_expand} ↓{bottom_expand} ←{left_expand} →{right_expand}"
            info_y = label_y + 20
            draw.text((max_left + 2, info_y), expand_info, 
                     fill=(200, 200, 200, 255), font=font_small)
            
            print(f"✅ 绘制最大可用区域: '{original_text}' (扩展: {left_expand},{top_expand},{right_expand},{bottom_expand})")
        
        # 保存调试图
        final_image = Image.alpha_composite(pil_image.convert('RGBA'), overlay)
        final_image = final_image.convert('RGB')
        cv2_image = cv2.cvtColor(np.array(final_image), cv2.COLOR_RGB2BGR)
        
        filename = "max_area_debug.png"
        output_path = os.path.join(self.output_dir, filename)
        cv2.imwrite(output_path, cv2_image)
        print(f"✅ 最大可用区域调试图已保存: {filename}")
        
        # 输出翻译文本分析
        print(f"\n📊 翻译文本分析:")
        for i, translation_item in enumerate(translation_results, 1):
            original_text = translation_item.get("original_text", "")
            japanese_text = translation_item.get("japanese_text", "")
            bbox = translation_item.get("bbox")
            max_area = translation_item.get("max_area", {})

            if bbox is None or len(bbox) < 4:
                print(f"  {i}. '{original_text}' -> '{japanese_text}'")
                print(f"     ⚠️ 缺少bbox信息，跳过尺寸分析")
                print()
                continue

            original_width = bbox[2]
            original_height = bbox[3]

            print(f"  {i}. '{original_text}' -> '{japanese_text}'")
            print(f"     原始区域: {original_width}×{original_height}px")
            print(f"     将使用OCR统一高度: {original_height}px")
            
            # 显示最大可用区域信息
            if max_area:
                left_expand = max_area.get("left_expand", 0)
                top_expand = max_area.get("top_expand", 0)
                right_expand = max_area.get("right_expand", 0)
                bottom_expand = max_area.get("bottom_expand", 0)
                max_width = original_width + left_expand + right_expand
                max_height = original_height + top_expand + bottom_expand
                print(f"     最大可用区域: {max_width}×{max_height}px (扩展: ←{left_expand} →{right_expand} ↑{top_expand} ↓{bottom_expand})")
            else:
                print(f"     最大可用区域: 未知")
            print()

    def save_analysis_report(self, image_path: str, gemini_analysis: Dict[str, Any], 
                            chinese_regions: List, other_regions: List, color_analysis: Dict[str, Any] = None) -> None:
        """保存分析报告"""
        report = {
            "image_path": image_path,
            "timestamp": datetime.now().isoformat(),
            "ocr_results": {
                "chinese_regions": len(chinese_regions),
                "other_regions": len(other_regions),
                "chinese_texts": [region.text for region in chinese_regions],
                "other_texts": [region.text for region in other_regions]
            },
            "color_analysis": color_analysis if color_analysis else {},
            "gemini_analysis": gemini_analysis,
            "analysis_summary": {
                "layout_mode": gemini_analysis.get("layout_analysis", {}).get("layout_mode", "unknown"),
                "main_alignment": gemini_analysis.get("layout_analysis", {}).get("main_alignment", "unknown"),
                "needs_translation_count": len(gemini_analysis.get("translation_analysis", {}).get("translation_results", [])),
                "no_translation_count": len(gemini_analysis.get("translation_analysis", {}).get("no_translation", [])),
                "gemini_confidence": gemini_analysis.get("confidence", 0.0),
                "color_analysis_count": len(color_analysis) if color_analysis else 0
            }
        }
        
        # 转换numpy类型为Python原生类型
        def convert_numpy_types(obj):
            if isinstance(obj, np.bool_):
                return bool(obj)
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {k: convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            else:
                return obj
        
        # 转换报告中的numpy类型
        report = convert_numpy_types(report)
        
        report_path = os.path.join(self.output_dir, "smart_analysis_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 分析报告已保存: {report_path}")
    
    def print_analysis_summary(self, gemini_analysis: Dict[str, Any]) -> None:
        """打印分析结果摘要"""
        print(f"\n📊 Gemini智能分析结果摘要")
        print(f"="*60)
        
        # 布局分析
        layout_analysis = gemini_analysis.get("layout_analysis", {})
        print(f"布局模式: {layout_analysis.get('layout_mode', 'unknown')}")
        print(f"主要对齐: {layout_analysis.get('main_alignment', 'unknown')}")
        
        # 翻译分析 - 简化的数据结构
        translation_analysis = gemini_analysis.get("translation_analysis", {})

        # 翻译结果
        translation_results = translation_analysis.get("translation_results", [])
        no_translation = translation_analysis.get("no_translation", [])

        print(f"需要翻译: {len(translation_results)} 个文字")
        print(f"无需翻译: {len(no_translation)} 个文字")

        if translation_results:
            print(f"\n需要翻译的文字:")
            for i, item in enumerate(translation_results, 1):
                original = item.get("original_text", "")
                japanese = item.get("japanese_text", "")
                color = item.get("color", [0, 0, 0])
                should_erase = item.get("should_erase", True)

                color_str = f"RGB({color[0]}, {color[1]}, {color[2]})" if isinstance(color, list) and len(color) >= 3 else str(color)
                erase_status = "需擦除" if should_erase else "不擦除"
                print(f"  {i}. '{original}' → '{japanese}' (颜色{color_str}, {erase_status})")

        if no_translation:
            print(f"\n无需翻译的文字:")
            for i, item in enumerate(no_translation, 1):
                original = item.get("original_text", "")
                reason = item.get("reason", "")
                print(f"  {i}. '{original}' - {reason}")
        
        confidence = gemini_analysis.get("confidence", 0.0)
        summary = gemini_analysis.get("summary", "")
        
        print(f"\n置信度: {confidence:.2f}")
        if summary:
            print(f"总结: {summary}")
        
        print(f"="*60)

    def print_gemini_analysis_details(self, gemini_analysis: Dict[str, Any]) -> None:
        """输出详细的Gemini简化分析结果（人类友好格式）"""
        print(f"\n" + "="*80)
        print(f"🤖 Gemini简化分析详细结果")
        print(f"="*80)

        # 1. 布局分析详情
        layout_analysis = gemini_analysis.get("layout_analysis", {})
        print(f"\n📐 布局分析:")
        print(f"  布局模式: {layout_analysis.get('layout_mode', '未知')}")
        print(f"  主要对齐: {layout_analysis.get('main_alignment', '未知')}")

        # 对齐分组详情
        alignment_groups = layout_analysis.get("alignment_groups", {})
        print(f"\n�� 对齐分组详情:")

        # 创建ID到原文的映射
        translation_analysis = gemini_analysis.get("translation_analysis", {})
        translation_results = translation_analysis.get("translation_results", [])
        no_translation = translation_analysis.get("no_translation", [])
        
        id_to_text = {}
        for item in translation_results:
            region_id = str(item.get("region_id", ""))
            original_text = item.get("original_text", "")
            id_to_text[region_id] = original_text
        
        for item in no_translation:
            region_id = str(item.get("region_id", ""))
            original_text = item.get("original_text", "")
            id_to_text[region_id] = original_text

        for group_type, groups in alignment_groups.items():
            if groups:
                group_type_name = {
                    "left_groups": "左对齐组",
                    "center_groups": "居中对齐组",
                    "right_groups": "右对齐组",
                    "distribution_groups": "分布对齐组"
                }.get(group_type, group_type)

                print(f"  {group_type_name}: {len(groups)} 组")
                for i, group in enumerate(groups, 1):
                    # 将ID转换为原文
                    text_group = []
                    for item_id in group:
                        text = id_to_text.get(str(item_id), f"ID{item_id}")
                        text_group.append(text)
                    print(f"    组{i}: {text_group}")

        # 2. 翻译结果详情
        translation_analysis = gemini_analysis.get("translation_analysis", {})
        translation_results = translation_analysis.get("translation_results", [])
        print(f"\n🔤 翻译结果详情:")
        print(f"  需要翻译: {len(translation_results)} 个")

        for i, result in enumerate(translation_results, 1):
            original = result.get("original_text", "")
            japanese = result.get("japanese_text", "")
            font_weight = result.get("font_weight", "未知")
            color = result.get("color", [0, 0, 0])
            should_erase = result.get("should_erase", True)
            text_align = result.get("text_align", "未知")
            # 空间分析信息
            space_analysis = result.get("space_analysis", {})

            color_str = f"RGB({color[0]}, {color[1]}, {color[2]})" if isinstance(color, list) and len(color) >= 3 else str(color)
            erase_status = "✅需擦除" if should_erase else "❌不擦除"

            print(f"  {i}. '{original}' → '{japanese}'")
            print(f"     字重: {font_weight} | 对齐: {text_align}")
            print(f"     颜色: {color_str} | {erase_status}")

            # 显示空间分析
            if space_analysis:
                translation_fits = space_analysis.get("translation_fits", True)
                overlap_risk = space_analysis.get("overlap_risk", False)

                fit_status = "✅适合" if translation_fits else "⚠️可能过长"
                overlap_status = "⚠️有重叠风险" if overlap_risk else "✅无重叠"
                print(f"     空间: {fit_status} | {overlap_status}")

            print()

        # 3. 不翻译文字详情
        no_translation = translation_analysis.get("no_translation", [])
        if no_translation:
            print(f"🚫 不翻译文字详情:")
            print(f"  不需要翻译: {len(no_translation)} 个")
            for i, item in enumerate(no_translation, 1):
                original = item.get("original_text", "")
                reason = item.get("reason", "")
                print(f"  {i}. '{original}' - {reason}")

        # 4. 总体评估
        confidence = gemini_analysis.get("confidence", 0.0)
        summary = gemini_analysis.get("summary", "")

        print(f"\n📈 总体评估:")
        print(f"  置信度: {confidence:.2f}")
        if summary:
            print(f"  总结: {summary}")

        print(f"="*80)

    def run_smart_translation_test(self, image_path: str) -> None:
        """运行智能翻译测试流程"""
        print(f"\n🚀 开始智能图像翻译测试流程")
        print(f"图片: {image_path}")
        print(f"模型: {self.model}")
        
        try:
            # 1. OCR识别提取文本区域
            chinese_regions, other_regions = self.extract_text_regions(image_path)
            
            # 2. 程序颜色识别分析
            color_analysis = self.analyze_colors_from_image(image_path, chinese_regions, other_regions)

            # 3. Gemini智能分析
            gemini_analysis = self.analyze_with_gemini(image_path, chinese_regions, other_regions)

            # 3.1 输出人类友好的Gemini分析结果
            self.print_gemini_analysis_details(gemini_analysis)
            
            # 4. 生成布局调试图
            self.generate_layout_debug_image(image_path, gemini_analysis, chinese_regions, other_regions)
            
            # 4.1 生成显示范围调试图
            self.generate_display_area_debug_image(image_path, gemini_analysis, chinese_regions, other_regions)
            
            # 4.2 生成最大可用区域调试图
            self.generate_max_area_debug_image(image_path, gemini_analysis, chinese_regions, other_regions)
            
            # 5. 执行智能翻译（传递颜色分析结果）
            self.execute_smart_translation(image_path, gemini_analysis, chinese_regions, color_analysis)
            
                         # 6. 保存分析报告（包含颜色分析结果）
            self.save_analysis_report(image_path, gemini_analysis, chinese_regions, other_regions, color_analysis)
            
            # 7. 输出分析摘要
            self.print_analysis_summary(gemini_analysis)
            
        except Exception as e:
            print(f"❌ 智能翻译测试流程失败: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="智能图像翻译测试程序")
    parser.add_argument("image", nargs="?", default="example.jpg", help="测试图片路径")
    
    args = parser.parse_args()
    
    # 检查图片是否存在
    if not os.path.exists(args.image):
        print(f"❌ 图片文件不存在: {args.image}")
        return
    
    # 创建测试器并运行
    tester = SmartTranslationTester()
    tester.run_smart_translation_test(args.image)

if __name__ == "__main__":
    main() 